<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.WRITE_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>


    <application
            android:name="MyApplication"
            android:allowBackup="true"
            android:screenOrientation="portrait"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/logo"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:supportsRtl="true"
            android:theme="@style/Theme.VCAMSX"
            android:allowNativeHeapPointerTagging="false"
            tools:targetApi="31">

        <activity
                android:name=".MainActivity"
                android:exported="true"
                android:label="@string/app_name"
                android:theme="@style/Theme.VCAMSX">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data
                android:name="xposedmodule"
                android:value="true"/>
        <!-- 模块的简介（在框架中显示） -->
        <meta-data
                android:name="xposeddescription"
                android:value="虚拟摄像" />
        <!-- 模块最低支持的Api版本 一般填54即可 -->
        <meta-data
                android:name="xposedminversion"
                android:value="54"/>

        <provider
                android:name=".utils.VideoProvider"
                android:authorities="com.wangyiheng.vcamsx.videoprovider"
                android:exported="true"
                android:grantUriPermissions="true">
        </provider>

        <provider
                android:name=".utils.MultiprocessSharedPreferences"
                android:authorities="com.wangyiheng.vcamsx.preferences"
                android:exported="true"/>

        <service android:name=".services.VcamsxForegroundService"
                 android:foregroundServiceType="mediaPlayback"
                 android:enabled="true"
                 android:exported="true" />
    </application>

</manifest>